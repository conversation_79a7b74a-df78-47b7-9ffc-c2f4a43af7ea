"use client"

import React from "react"

import { useState, useRef, use<PERSON><PERSON>back, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Download, Palette, Edit3, Layout, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ge, Ch<PERSON>ronDown, Upload } from "lucide-react"
import { useTheme } from "next-themes"
import html2canvas from "html2canvas"
import { TravelData, TemplateElement, TemplateLayout, ElementStyle } from "@/components/template/types"
import { defaultTravelData, templateFormats, fontOptions, fontWeights, defaultElementStyle, defaultCurrency, currencyOptions, getTravelPresetData, travelCategories, popularDestinations } from "@/components/template/constants"
import { DraggableElement } from "@/components/template/draggable-element"
import { BrandKitModal } from "@/components/brand-kit-modal"
import { useBrandKit } from "@/hooks/use-brand-kit"





export default function EnhancedTravelTemplateGenerator() {
  const [travelData, setTravelData] = useState<TravelData>(defaultTravelData)
  const [currentLayout, setCurrentLayout] = useState<TemplateLayout>(templateFormats[0])
  const [selectedElement, setSelectedElement] = useState<string | null>(null)
  const [isDragging, setIsDragging] = useState<string | null>(null)
  const [isResizing, setIsResizing] = useState<string | null>(null)
  const [resizeHandle, setResizeHandle] = useState<string | null>(null)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  const [resizeStartData, setResizeStartData] = useState<{
    size: { width: number; height: number }
    position: { x: number; y: number }
    mouse: { x: number; y: number }
  } | null>(null)
  const [isExporting, setIsExporting] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [showGrid, setShowGrid] = useState(false)
  const [showGuidelines, setShowGuidelines] = useState(true)
  const [uploadedImages, setUploadedImages] = useState<string[]>([])
  const [suggestedImages, setSuggestedImages] = useState<{url: string, alt: string}[]>([])
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false)
  const [colorPalettes, setColorPalettes] = useState<{name: string, colors: string[]}[]>([])
  const [isLoadingColors, setIsLoadingColors] = useState(false)
  const [editingElement, setEditingElement] = useState<string | null>(null)
  const [editingText, setEditingText] = useState("")
  const templateRef = useRef<HTMLDivElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { theme, setTheme } = useTheme()
  
  // Brand Kit functionality
  const {
    brandKit,
    saveBrandKit,
    hasBrandKit,
    getPrimaryColor,
    getSecondaryColor,
    getAccentColor,
    getPrimaryLogo,
    getHeadlineFont,
    getBodyFont,
    getBrandColorPalette,
    injectBrandFonts,
  } = useBrandKit()

  const handleInputChange = (field: keyof TravelData, value: string) => {
    setTravelData((prev) => ({ ...prev, [field]: value }))
  }

  const handleLoadPreset = (category: string, destination: string) => {
    const presetData = getTravelPresetData(category, destination)
    setTravelData(presetData)
  }

  const handleLayoutChange = (layoutId: string) => {
    const layout = templateFormats.find((l) => l.id === layoutId)
    if (layout) {
      setCurrentLayout(layout)
      setSelectedElement(null)
    }
  }

  const handleElementStyleChange = (elementId: string, styleProperty: keyof ElementStyle, value: any) => {
    setCurrentLayout((prev) => ({
      ...prev,
      elements: prev.elements.map((el) =>
        el.id === elementId ? { ...el, style: { ...el.style, [styleProperty]: value } } : el,
      ),
    }))
  }

  const handleElementPositionChange = (elementId: string, position: { x: number; y: number }) => {
    setCurrentLayout((prev) => ({
      ...prev,
      elements: prev.elements.map((el) => (el.id === elementId ? { ...el, position } : el)),
    }))
  }

  const handleElementSizeChange = (elementId: string, size: { width: number; height: number }) => {
    setCurrentLayout((prev) => ({
      ...prev,
      elements: prev.elements.map((el) => (el.id === elementId ? { ...el, size } : el)),
    }))
  }

  const handleBackgroundStyleChange = (property: string, value: any) => {
    setCurrentLayout((prev) => ({
      ...prev,
      backgroundStyle: { ...prev.backgroundStyle, [property]: value },
    }))
  }

  const handleDeleteElement = (elementId: string) => {
    setCurrentLayout((prev) => ({
      ...prev,
      elements: prev.elements.filter((el) => el.id !== elementId),
    }))
    setSelectedElement(null)
  }

  const handleAddTextElement = () => {
    const newElement: TemplateElement = {
      id: `text-${Date.now()}`,
      type: "text",
      content: "New Text",
      position: { x: 50, y: 50 },
      size: { width: 200, height: 40 },
      style: { ...defaultElementStyle }
    }
    setCurrentLayout((prev) => ({
      ...prev,
      elements: [...prev.elements, newElement]
    }))
    setSelectedElement(newElement.id)
  }

  const handleDuplicateElement = (elementId: string) => {
    const elementToDuplicate = currentLayout.elements.find(el => el.id === elementId)
    if (!elementToDuplicate) return

    const newElement: TemplateElement = {
      ...elementToDuplicate,
      id: `${elementToDuplicate.type}-${Date.now()}`,
      position: {
        x: elementToDuplicate.position.x + 20,
        y: elementToDuplicate.position.y + 20
      }
    }
    setCurrentLayout((prev) => ({
      ...prev,
      elements: [...prev.elements, newElement]
    }))
    setSelectedElement(newElement.id)
  }

  const handleDoubleClick = (elementId: string) => {
    const element = currentLayout.elements.find((el) => el.id === elementId)
    if (!element || element.type !== "text") return

    setEditingElement(elementId)
    setEditingText(element.content)
  }

  const handleTextEditSave = () => {
    if (!editingElement) return

    setCurrentLayout((prev) => ({
      ...prev,
      elements: prev.elements.map((el) =>
        el.id === editingElement ? { ...el, content: editingText } : el
      ),
    }))
    setEditingElement(null)
    setEditingText("")
  }

  const handleTextEditCancel = () => {
    setEditingElement(null)
    setEditingText("")
  }

  const handleResizeStart = (e: React.MouseEvent | React.TouchEvent, elementId: string, handle: string) => {
    const element = currentLayout.elements.find((el) => el.id === elementId)
    if (!element || element.locked) return

    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY

    setIsResizing(elementId)
    setResizeHandle(handle)
    setResizeStartData({
      size: { ...element.size },
      position: { ...element.position },
      mouse: { x: clientX, y: clientY }
    })
    
    e.preventDefault()
    e.stopPropagation()
  }

  const handleResizeMove = useCallback(
    (e: MouseEvent | TouchEvent) => {
      if (!isResizing || !resizeStartData || !resizeHandle) return

      const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX
      const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY

      const deltaX = clientX - resizeStartData.mouse.x
      const deltaY = clientY - resizeStartData.mouse.y
      
      const scale = Math.floor(currentLayout.displaySize.width * 1.5) / currentLayout.displaySize.width
      const scaledDeltaX = deltaX / scale
      const scaledDeltaY = deltaY / scale

      let newSize = { ...resizeStartData.size }
      let newPosition = { ...resizeStartData.position }

      switch (resizeHandle) {
        case 'se': // bottom-right
          newSize.width = Math.max(20, resizeStartData.size.width + scaledDeltaX)
          newSize.height = Math.max(10, resizeStartData.size.height + scaledDeltaY)
          break
        case 'sw': // bottom-left
          newSize.width = Math.max(20, resizeStartData.size.width - scaledDeltaX)
          newSize.height = Math.max(10, resizeStartData.size.height + scaledDeltaY)
          newPosition.x = resizeStartData.position.x + Math.min(scaledDeltaX, resizeStartData.size.width - 20)
          break
        case 'ne': // top-right
          newSize.width = Math.max(20, resizeStartData.size.width + scaledDeltaX)
          newSize.height = Math.max(10, resizeStartData.size.height - scaledDeltaY)
          newPosition.y = resizeStartData.position.y + Math.min(scaledDeltaY, resizeStartData.size.height - 10)
          break
        case 'nw': // top-left
          newSize.width = Math.max(20, resizeStartData.size.width - scaledDeltaX)
          newSize.height = Math.max(10, resizeStartData.size.height - scaledDeltaY)
          newPosition.x = resizeStartData.position.x + Math.min(scaledDeltaX, resizeStartData.size.width - 20)
          newPosition.y = resizeStartData.position.y + Math.min(scaledDeltaY, resizeStartData.size.height - 10)
          break
        case 'e': // right edge
          newSize.width = Math.max(20, resizeStartData.size.width + scaledDeltaX)
          break
        case 'w': // left edge
          newSize.width = Math.max(20, resizeStartData.size.width - scaledDeltaX)
          newPosition.x = resizeStartData.position.x + Math.min(scaledDeltaX, resizeStartData.size.width - 20)
          break
        case 's': // bottom edge
          newSize.height = Math.max(10, resizeStartData.size.height + scaledDeltaY)
          break
        case 'n': // top edge
          newSize.height = Math.max(10, resizeStartData.size.height - scaledDeltaY)
          newPosition.y = resizeStartData.position.y + Math.min(scaledDeltaY, resizeStartData.size.height - 10)
          break
      }

      // Keep element within canvas bounds
      newPosition.x = Math.max(0, Math.min(currentLayout.displaySize.width - newSize.width, newPosition.x))
      newPosition.y = Math.max(0, Math.min(currentLayout.displaySize.height - newSize.height, newPosition.y))
      newSize.width = Math.min(currentLayout.displaySize.width - newPosition.x, newSize.width)
      newSize.height = Math.min(currentLayout.displaySize.height - newPosition.y, newSize.height)

      setCurrentLayout((prev) => ({
        ...prev,
        elements: prev.elements.map((el) =>
          el.id === isResizing ? { ...el, size: newSize, position: newPosition } : el
        ),
      }))
    },
    [isResizing, resizeStartData, resizeHandle, currentLayout]
  )

  const handleResizeEnd = useCallback(() => {
    setIsResizing(null)
    setResizeHandle(null)
    setResizeStartData(null)
  }, [])

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const imageUrl = e.target?.result as string
        setUploadedImages((prev) => [...prev, imageUrl])
        handleBackgroundStyleChange("backgroundImage", imageUrl)
      }
      reader.readAsDataURL(file)
    }
  }

  const fetchImageSuggestions = async (destination: string) => {
    if (!destination.trim()) return
    
    setIsLoadingSuggestions(true)
    try {
      // Using Unsplash API for free travel images
      const searchQuery = `${destination} travel landscape scenic`
      const response = await fetch(`https://api.unsplash.com/search/photos?query=${encodeURIComponent(searchQuery)}&per_page=6&orientation=landscape&client_id=aUtOGBkG-YYjRk5p9I5yZgTNj4wH_lSFwJhD8a8PVlE`)
      
      if (response.ok) {
        const data = await response.json()
        const images = data.results.map((img: any) => ({
          url: img.urls.regular,
          alt: img.alt_description || `${destination} landscape`
        }))
        setSuggestedImages(images)
      } else {
        // Fallback to generic travel images
        setSuggestedImages([
          { url: 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=800', alt: 'Travel destination' },
          { url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800', alt: 'Mountain landscape' },
          { url: 'https://images.unsplash.com/photo-1507525428034-b723cf961d3e?w=800', alt: 'Beach destination' },
        ])
      }
    } catch (error) {
      console.error('Failed to fetch images:', error)
      // Fallback images
      setSuggestedImages([
        { url: 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=800', alt: 'Travel destination' },
        { url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800', alt: 'Mountain landscape' },
      ])
    } finally {
      setIsLoadingSuggestions(false)
    }
  }

  const generateColorPalettes = async (destination: string) => {
    if (!destination.trim()) return

    setIsLoadingColors(true)
    try {
      // Generate themed color palettes based on destination type
      const destinationLower = destination.toLowerCase()
      let palettes: {name: string, colors: string[]}[] = []

      // Beach/coastal destinations
      if (destinationLower.includes('beach') || destinationLower.includes('coast') || destinationLower.includes('island') || destinationLower.includes('maldives') || destinationLower.includes('goa')) {
        palettes = [
          { name: 'Ocean Breeze', colors: ['#0EA5E9', '#06B6D4', '#F0F9FF', '#1E293B', '#0F766E'] },
          { name: 'Sunset Beach', colors: ['#F97316', '#FB923C', '#FED7AA', '#1F2937', '#DC2626'] },
          { name: 'Tropical Paradise', colors: ['#10B981', '#34D399', '#D1FAE5', '#374151', '#059669'] },
        ]
      }
      // Mountain destinations
      else if (destinationLower.includes('mountain') || destinationLower.includes('hill') || destinationLower.includes('manali') || destinationLower.includes('shimla') || destinationLower.includes('ladakh')) {
        palettes = [
          { name: 'Mountain Mist', colors: ['#64748B', '#94A3B8', '#F1F5F9', '#1E293B', '#475569'] },
          { name: 'Alpine Glow', colors: ['#7C3AED', '#A78BFA', '#EDE9FE', '#1F2937', '#5B21B6'] },
          { name: 'Forest Peak', colors: ['#059669', '#10B981', '#D1FAE5', '#374151', '#047857'] },
        ]
      }
      // City/urban destinations
      else if (destinationLower.includes('city') || destinationLower.includes('delhi') || destinationLower.includes('mumbai') || destinationLower.includes('bangalore') || destinationLower.includes('chennai')) {
        palettes = [
          { name: 'Urban Chic', colors: ['#374151', '#6B7280', '#F9FAFB', '#1F2937', '#111827'] },
          { name: 'Neon Nights', colors: ['#8B5CF6', '#A78BFA', '#EDE9FE', '#1F2937', '#7C3AED'] },
          { name: 'Golden Hour', colors: ['#F59E0B', '#FCD34D', '#FEF3C7', '#1F2937', '#D97706'] },
        ]
      }
      // Desert destinations
      else if (destinationLower.includes('desert') || destinationLower.includes('rajasthan') || destinationLower.includes('jaisalmer') || destinationLower.includes('jodhpur')) {
        palettes = [
          { name: 'Desert Sands', colors: ['#F59E0B', '#FCD34D', '#FEF3C7', '#1F2937', '#D97706'] },
          { name: 'Royal Palace', colors: ['#DC2626', '#F87171', '#FEE2E2', '#1F2937', '#B91C1C'] },
          { name: 'Sunset Dunes', colors: ['#EA580C', '#FB923C', '#FED7AA', '#1F2937', '#C2410C'] },
        ]
      }
      // Default travel palettes
      else {
        palettes = [
          { name: 'Wanderlust', colors: ['#3B82F6', '#60A5FA', '#DBEAFE', '#1F2937', '#2563EB'] },
          { name: 'Adventure', colors: ['#EF4444', '#F87171', '#FEE2E2', '#1F2937', '#DC2626'] },
          { name: 'Explore', colors: ['#10B981', '#34D399', '#D1FAE5', '#1F2937', '#059669'] },
          { name: 'Journey', colors: ['#8B5CF6', '#A78BFA', '#EDE9FE', '#1F2937', '#7C3AED'] },
        ]
      }

      // Add universal travel palettes
      palettes.push(
        { name: 'Classic Travel', colors: ['#1E40AF', '#3B82F6', '#DBEAFE', '#1F2937', '#1D4ED8'] },
        { name: 'Vintage Map', colors: ['#92400E', '#D97706', '#FED7AA', '#1F2937', '#B45309'] },
        { name: 'Fresh Mint', colors: ['#059669', '#10B981', '#D1FAE5', '#1F2937', '#047857'] }
      )

      setColorPalettes(palettes)
    } catch (error) {
      console.error('Failed to generate color palettes:', error)
      // Fallback palettes
      setColorPalettes([
        { name: 'Default Blue', colors: ['#3B82F6', '#60A5FA', '#DBEAFE', '#1F2937', '#2563EB'] },
        { name: 'Travel Orange', colors: ['#F97316', '#FB923C', '#FED7AA', '#1F2937', '#EA580C'] },
        { name: 'Nature Green', colors: ['#10B981', '#34D399', '#D1FAE5', '#1F2937', '#059669'] },
      ])
    } finally {
      setIsLoadingColors(false)
    }
  }

  // Apply Brand Kit function
  const applyBrandKit = () => {
    if (!hasBrandKit()) {
      console.warn("No brand kit available to apply")
      return
    }

    let updatedLayout = { ...currentLayout }

    // Apply brand colors to background
    const primaryColor = getPrimaryColor()
    const secondaryColor = getSecondaryColor()
    const accentColor = getAccentColor()

    // Update background color to primary
    updatedLayout.backgroundStyle = {
      ...updatedLayout.backgroundStyle,
      backgroundColor: primaryColor,
    }

    // Apply brand fonts to text elements
    const headlineFont = getHeadlineFont()
    const bodyFont = getBodyFont()

    updatedLayout.elements = updatedLayout.elements.map((element) => {
      let updatedElement = { ...element }

      // Apply font based on element type and content
      if (element.type === "text") {
        const isHeadline = element.id.includes("title") || element.id.includes("destination") || 
                          element.style.fontSize > 20 || element.style.fontWeight === "700" || 
                          element.style.fontWeight === "800"
        
        if (isHeadline && headlineFont) {
          updatedElement.style = {
            ...updatedElement.style,
            fontFamily: headlineFont.fontFamily,
          }
        } else if (!isHeadline && bodyFont) {
          updatedElement.style = {
            ...updatedElement.style,
            fontFamily: bodyFont.fontFamily,
          }
        }
      }

      // Apply brand colors to text elements
      if (element.type === "text" || element.type === "price") {
        // Apply different colors based on element hierarchy
        if (element.id.includes("title") || element.id === "destination") {
          updatedElement.style = {
            ...updatedElement.style,
            color: "#FFFFFF", // White text on primary background
          }
        } else if (element.id.includes("price")) {
          updatedElement.style = {
            ...updatedElement.style,
            color: accentColor,
          }
        } else {
          updatedElement.style = {
            ...updatedElement.style,
            color: secondaryColor,
          }
        }
      }

      return updatedElement
    })

    // Add primary logo to the template
    const primaryLogo = getPrimaryLogo()
    console.log("Primary logo found:", primaryLogo) // Debug log
    if (primaryLogo) {
      // Remove any existing brand logo first
      updatedLayout.elements = updatedLayout.elements.filter(el => !el.id.startsWith("brand-logo-"))
      
      const logoElement: TemplateElement = {
        id: `brand-logo-${Date.now()}`,
        type: "image",
        content: primaryLogo.url,
        position: { x: 20, y: 20 }, // Top left corner instead of top right
        size: { width: 80, height: 80 }, // Larger size for better visibility
        style: {
          ...defaultElementStyle,
          backgroundColor: "transparent",
          borderRadius: 8,
          opacity: 0.9,
        },
      }

      updatedLayout.elements = [...updatedLayout.elements, logoElement]
      console.log("Logo element added:", logoElement) // Debug log
    } else {
      console.log("No primary logo found in brand kit") // Debug log
    }

    // Apply the updated layout
    setCurrentLayout(updatedLayout)
    
    // Inject brand fonts
    injectBrandFonts()

    console.log("Brand kit applied successfully!")
  }

  const handleMouseDown = (e: React.MouseEvent, elementId: string) => {
    if (e.button !== 0) return // Only left click

    const element = currentLayout.elements.find((el) => el.id === elementId)
    if (!element || element.locked) return

    const rect = e.currentTarget.getBoundingClientRect()
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    })
    setIsDragging(elementId)
    setSelectedElement(elementId)
    e.preventDefault()
  }

  const handleTouchStart = (e: React.TouchEvent, elementId: string) => {
    const element = currentLayout.elements.find((el) => el.id === elementId)
    if (!element || element.locked) return

    const touch = e.touches[0]
    const rect = e.currentTarget.getBoundingClientRect()
    setDragOffset({
      x: touch.clientX - rect.left,
      y: touch.clientY - rect.top,
    })
    setIsDragging(elementId)
    setSelectedElement(elementId)
    e.preventDefault()
  }

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging || !templateRef.current) return

      const templateRect = templateRef.current.getBoundingClientRect()
      const scale = Math.floor(currentLayout.displaySize.width * 1.5) / currentLayout.displaySize.width
      const maxX = Math.floor(currentLayout.displaySize.width * 1.5) - 50
      const maxY = Math.floor(currentLayout.displaySize.height * 1.5) - 20

      const newX = Math.max(0, Math.min(maxX, e.clientX - templateRect.left - dragOffset.x)) / scale
      const newY = Math.max(0, Math.min(maxY, e.clientY - templateRect.top - dragOffset.y)) / scale

      handleElementPositionChange(isDragging, { x: newX, y: newY })
    },
    [isDragging, dragOffset, currentLayout],
  )

  const handleTouchMove = useCallback(
    (e: TouchEvent) => {
      if (!isDragging || !templateRef.current) return

      const touch = e.touches[0]
      const templateRect = templateRef.current.getBoundingClientRect()
      const scale = Math.floor(currentLayout.displaySize.width * 1.5) / currentLayout.displaySize.width
      const maxX = Math.floor(currentLayout.displaySize.width * 1.5) - 50
      const maxY = Math.floor(currentLayout.displaySize.height * 1.5) - 20

      const newX = Math.max(0, Math.min(maxX, touch.clientX - templateRect.left - dragOffset.x)) / scale
      const newY = Math.max(0, Math.min(maxY, touch.clientY - templateRect.top - dragOffset.y)) / scale

      handleElementPositionChange(isDragging, { x: newX, y: newY })
    },
    [isDragging, dragOffset, currentLayout],
  )

  const handleMouseUp = useCallback(() => {
    setIsDragging(null)
  }, [])

  // Add event listeners for drag functionality
  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove)
      document.addEventListener("mouseup", handleMouseUp)
      document.addEventListener("touchmove", handleTouchMove, { passive: false })
      document.addEventListener("touchend", handleMouseUp)
      return () => {
        document.removeEventListener("mousemove", handleMouseMove)
        document.removeEventListener("mouseup", handleMouseUp)
        document.removeEventListener("touchmove", handleTouchMove)
        document.removeEventListener("touchend", handleMouseUp)
      }
    }
  }, [isDragging, handleMouseMove, handleMouseUp, handleTouchMove])

  // Inject brand fonts when brand kit is loaded
  useEffect(() => {
    if (brandKit?.fonts && brandKit.fonts.length > 0) {
      injectBrandFonts()
    }
  }, [brandKit, injectBrandFonts])

  // Add event listeners for resize functionality
  React.useEffect(() => {
    if (isResizing) {
      document.addEventListener("mousemove", handleResizeMove)
      document.addEventListener("mouseup", handleResizeEnd)
      document.addEventListener("touchmove", handleResizeMove, { passive: false })
      document.addEventListener("touchend", handleResizeEnd)
      return () => {
        document.removeEventListener("mousemove", handleResizeMove)
        document.removeEventListener("mouseup", handleResizeEnd)
        document.removeEventListener("touchmove", handleResizeMove)
        document.removeEventListener("touchend", handleResizeEnd)
      }
    }
  }, [isResizing, handleResizeMove, handleResizeEnd])

  const exportTemplate = async () => {
    if (templateRef.current) {
      setIsExporting(true)
      setSelectedElement(null) // Deselect any element before exporting

      // Brief timeout to allow UI to update
      await new Promise(resolve => setTimeout(resolve, 100))

      try {
        const canvas = await html2canvas(templateRef.current, {
          scale: 2, // Use a fixed scale for better quality
          useCORS: true,
          backgroundColor: currentLayout.backgroundStyle.backgroundColor,
          logging: false,
          onclone: (document) => {
            // Remove selection-related elements and styles from the clone
            const clone = document.documentElement
            clone.querySelectorAll('.selected').forEach(el => el.classList.remove('selected'))
            clone.querySelectorAll('[style*="border-color: #3b82f6"]').forEach(el => {
              ;(el as HTMLElement).style.border = '1px solid transparent'
              ;(el as HTMLElement).style.outline = 'none'
            })
            clone.querySelectorAll('[class*="-resize"]').forEach(el => {
              ;(el as HTMLElement).style.display = 'none'
            })
            clone.querySelectorAll('.debug-overlay').forEach(el => {
              ;(el as HTMLElement).style.display = 'none'
            })
            
            // Force vertical alignment for text elements
            clone.querySelectorAll('.template-element > div').forEach(el => {
              const element = el as HTMLElement
              if (element.style.display === 'flex') {
                element.style.alignItems = 'center'
              }
            })
          }
        })
        
        const link = document.createElement("a")
        link.download = `${travelData.title.replace(/\s+/g, "-").toLowerCase()}-${currentLayout.id}.png`
        link.href = canvas.toDataURL("image/png")
        link.click()

      } catch (error) {
        console.error("Export failed:", error)
        alert("There was an error exporting the image. Please try again.")
      } finally {
        setIsExporting(false)
      }
    }
  }

  const exportWithResolution = (resolution: 'original' | 'high' | 'print' | 'web') => {
    // This function is now a proxy to the main exportTemplate function
    // You can add more complex logic here if needed for different resolutions
    exportTemplate()
  }

  const selectedElementData = selectedElement ? currentLayout.elements.find((el) => el.id === selectedElement) : null

  // Keyboard shortcuts and text editing
  useEffect(() => {
    const handleKeyboardShortcuts = (e: KeyboardEvent) => {
      if (!selectedElement || !selectedElementData) return

      // Delete key to delete selected element
      if (e.key === 'Delete' && !selectedElementData.locked) {
        e.preventDefault()
        handleDeleteElement(selectedElement)
      }

      // Ctrl+D to duplicate selected element
      if (e.ctrlKey && e.key === 'd' && !selectedElementData.locked) {
        e.preventDefault()
        handleDuplicateElement(selectedElement)
      }

      // Arrow keys to move selected element
      if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.key) && !selectedElementData.locked) {
        e.preventDefault()
        const moveAmount = e.shiftKey ? 10 : 1
        let newX = selectedElementData.position.x
        let newY = selectedElementData.position.y

        switch (e.key) {
          case 'ArrowLeft':
            newX = Math.max(0, newX - moveAmount)
            break
          case 'ArrowRight':
            newX = Math.min(currentLayout.displaySize.width - selectedElementData.size.width, newX + moveAmount)
            break
          case 'ArrowUp':
            newY = Math.max(0, newY - moveAmount)
            break
          case 'ArrowDown':
            newY = Math.min(currentLayout.displaySize.height - selectedElementData.size.height, newY + moveAmount)
            break
        }

        handleElementPositionChange(selectedElement, { x: newX, y: newY })
      }
    }

    const handleTextEditingKeys = (e: KeyboardEvent) => {
      if (editingElement) {
        if (e.key === "Enter" && (e.ctrlKey || e.metaKey)) {
          e.preventDefault()
          handleTextEditSave()
        } else if (e.key === "Escape") {
          e.preventDefault()
          handleTextEditCancel()
        }
      }
    }

    const handleKeyDown = (e: KeyboardEvent) => {
      handleKeyboardShortcuts(e)
      handleTextEditingKeys(e)
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [selectedElement, selectedElementData, currentLayout, handleDeleteElement, handleElementPositionChange, editingElement, editingText, handleTextEditSave, handleTextEditCancel, handleDuplicateElement])

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-[50]">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                Travel Instagram Generator
              </h1>
            </div>
            
            <div className="flex items-center gap-4">
              {/* Theme Toggle */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
                className="w-9 h-9 p-0"
              >
                <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
                <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
                <span className="sr-only">Toggle theme</span>
              </Button>
              
              {/* Desktop Actions */}
              <div className="hidden md:flex items-center gap-2">
                                 <Button
                   variant="outline"
                   size="sm"
                   onClick={() => {
                     if (templateRef.current) {
                       // Get the stylesheets from the current document
                       const stylesheets = Array.from(document.styleSheets)
                         .map((sheet) => {
                           try {
                             if (sheet.href) {
                               return `<link rel="stylesheet" href="${sheet.href}" />`;
                             }
                           } catch (e) {}
                           return "";
                         })
                         .join("");
                       // Add Tailwind CDN as fallback
                       const tailwindCdn = `<link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">`;

                       // Create the template HTML with absolute URLs
                       const backgroundImageUrl = currentLayout.backgroundStyle.backgroundImage 
                         ? currentLayout.backgroundStyle.backgroundImage.startsWith('/') 
                           ? `${window.location.origin}${currentLayout.backgroundStyle.backgroundImage}`
                           : currentLayout.backgroundStyle.backgroundImage
                         : 'none';

                       const templateHTML = `
                         <div
                           class="relative shadow-lg overflow-hidden"
                           style="
                             width: ${Math.floor(currentLayout.displaySize.width * 1.5)}px;
                             height: ${Math.floor(currentLayout.displaySize.height * 1.5)}px;
                             background-color: ${currentLayout.backgroundStyle.backgroundColor};
                             background-image: ${backgroundImageUrl !== 'none' ? `url('${backgroundImageUrl}')` : 'none'};
                             background-size: cover;
                             background-position: center;
                           "
                         >
                           ${currentLayout.backgroundStyle.overlay && currentLayout.backgroundStyle.backgroundImage ? `
                             <div
                               class="absolute inset-0 bg-black"
                               style="opacity: ${currentLayout.backgroundStyle.overlayOpacity};"
                             ></div>
                           ` : ''}
                           
                           ${currentLayout.elements.map((element) => {
                             const content = element.field && travelData[element.field] ? travelData[element.field] : element.content;
                             if (!content) return '';
                             const scale = Math.floor(currentLayout.displaySize.width * 1.5) / currentLayout.displaySize.width;
                             
                             const elementStyle = `
                               position: absolute;
                               left: ${element.position.x * scale}px;
                               top: ${element.position.y * scale}px;
                               width: ${element.size.width * scale}px;
                               height: ${element.size.height * scale}px;
                               font-family: ${element.style.fontFamily};
                               font-size: ${element.style.fontSize * scale}px;
                               font-weight: ${element.style.fontWeight};
                               color: ${element.style.color};
                               background-color: ${element.style.backgroundColor};
                               padding: ${element.style.padding * scale}px;
                               border-radius: ${element.style.borderRadius * scale}px;
                               text-align: ${element.style.textAlign};
                               opacity: ${element.style.opacity};
                               display: flex;
                               align-items: ${element.type === "price" ? "center" : "flex-start"};
                               justify-content: ${element.style.textAlign === "center" ? "center" : element.style.textAlign === "right" ? "flex-end" : "flex-start"};
                               line-height: ${(element.style as any).lineHeight || "normal"};
                               text-transform: ${(element.style as any).textTransform || "none"};
                               white-space: ${element.type === "text" && content.includes("\n") ? "pre-wrap" : "normal"};
                               overflow: hidden;
                               border: 2px solid transparent;
                               outline: none;
                               z-index: 1;
                               user-select: none;
                             `;
                             
                             return `
                               <div style="${elementStyle}">
                                 ${element.type === "price" && !content.match(/^[₹$€£¥A\$C\$]/) ? (travelData.currency || defaultCurrency) : ""}${content}
                               </div>
                             `;
                           }).join('')}
                         </div>
                       `;

                       const html = `
                         <html>
                           <head>
                             <title>Template Preview</title>
                             ${tailwindCdn}
                             ${stylesheets}
                             <style>body { background: #f5f5f5; display: flex; justify-content: center; align-items: center; min-height: 100vh; margin: 0; padding: 20px; }</style>
                           </head>
                           <body>
                             ${templateHTML}
                           </body>
                         </html>
                       `;
                       const blob = new Blob([html], { type: "text/html" });
                       const url = URL.createObjectURL(blob);
                       const a = document.createElement("a");
                       a.href = url;
                       a.target = "_blank";
                       a.rel = "noopener noreferrer";
                       document.body.appendChild(a);
                       a.click();
                       setTimeout(() => {
                         document.body.removeChild(a);
                         URL.revokeObjectURL(url);
                       }, 1000);
                     }
                   }}
                 >
                   <Eye className="w-4 h-4 mr-2" />
                   Preview
                 </Button>
                <Select value={currentLayout.id} onValueChange={handleLayoutChange}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {templateFormats.map((layout) => (
                      <SelectItem key={layout.id} value={layout.id}>
                        {layout.name.split('\n')[0]}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <BrandKitModal brandKit={brandKit} onBrandKitUpdate={saveBrandKit}>
                  <Button variant="outline" size="sm">
                    <Sparkles className="w-4 h-4 mr-2" />
                    Brand Kit
                  </Button>
                </BrandKitModal>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button 
                      size="sm" 
                      disabled={isExporting}
                    >
                      <Download className="w-4 h-4 mr-2" />
                      {isExporting ? "Exporting..." : "Export Image"}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => exportTemplate()}>
                      <Download className="w-4 h-4 mr-2" />
                      Download Image
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* Mobile Menu */}
              <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
                <SheetTrigger asChild>
                  <Button variant="outline" size="sm" className="md:hidden">
                    <Menu className="h-4 w-4" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-80">
                  <div className="py-4 space-y-4">
                    <div className="space-y-2">
                      <Label>Template Format</Label>
                      <Select value={currentLayout.id} onValueChange={handleLayoutChange}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {templateFormats.map((layout) => (
                            <SelectItem key={layout.id} value={layout.id}>
                              {layout.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Button className="w-full" onClick={() => exportTemplate()} disabled={isExporting}>
                        <Download className="w-4 h-4 mr-2" />
                        {isExporting ? "Exporting..." : "Export"}
                      </Button>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="px-4 sm:px-6 lg:px-8 py-6">
        <div className="max-w-7xl mx-auto">
          {/* Mobile-first responsive layout */}
          <div className="flex flex-col lg:flex-row gap-4 lg:gap-6">
            
            {/* Travel Information - Mobile: Full width, Desktop: Sidebar */}
            <div className="w-full lg:w-80 lg:flex-shrink-0 order-1 lg:order-1">
              <div className="lg:sticky lg:top-24 space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Edit3 className="w-5 h-5" />
                      Travel Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Quick Preset Selection */}
                    <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
                      <Label className="text-sm font-semibold text-gray-700 mb-3 block">🚀 Quick Start Templates</Label>
                      <div className="grid grid-cols-2 gap-2 mb-3">
                        {travelCategories.map((category) => (
                          <div key={category.id} className="space-y-1">
                            <div className="text-xs font-medium text-gray-600 text-center">{category.emoji} {category.name}</div>
                            <div className="grid gap-1">
                              {popularDestinations.map((destination) => (
                                <Button
                                  key={`${category.id}-${destination.id}`}
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleLoadPreset(category.id, destination.id)}
                                  className="h-8 text-xs px-2 hover:bg-blue-100 border-blue-200"
                                >
                                  {destination.emoji} {destination.name.split(',')[0]}
                                </Button>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                      <div className="text-xs text-gray-500 text-center">Click to load preset data for instant promo creation</div>
                    </div>

                    <Separator />
                    <div>
                      <Label htmlFor="title">Package Title</Label>
                      <Input
                        id="title"
                        value={travelData.title}
                        onChange={(e) => handleInputChange("title", e.target.value)}
                        placeholder="Enter package title"
                      />
                    </div>

                    <div>
                      <Label htmlFor="destination">Destination</Label>
                      <Input
                        id="destination"
                        value={travelData.destination}
                        onChange={(e) => handleInputChange("destination", e.target.value)}
                        placeholder="Enter destination"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <Label htmlFor="date">Travel Date</Label>
                        <Input
                          id="date"
                          value={travelData.date}
                          onChange={(e) => handleInputChange("date", e.target.value)}
                          placeholder="Enter dates"
                        />
                      </div>
                      <div>
                        <Label htmlFor="price">Price</Label>
                        <div className="flex gap-2">
                          <Select
                            value={travelData.currency || defaultCurrency}
                            onValueChange={(value) => handleInputChange("currency", value)}
                          >
                            <SelectTrigger className="w-[100px]">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {currencyOptions.map((currency) => (
                                <SelectItem key={currency.code} value={currency.symbol}>
                                  {currency.symbol} ({currency.code})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <Input
                            id="price"
                            value={travelData.price}
                            onChange={(e) => handleInputChange("price", e.target.value)}
                            placeholder="Enter price (e.g., 19,000)"
                            className="flex-1"
                          />
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="packageDetails">Package Details</Label>
                      <Input
                        id="packageDetails"
                        value={travelData.packageDetails}
                        onChange={(e) => handleInputChange("packageDetails", e.target.value)}
                        placeholder="e.g., 7 Days / 6 Nights"
                      />
                    </div>

                    <div>
                      <Label htmlFor="plan">Travel Plan</Label>
                      <Textarea
                        id="plan"
                        value={travelData.plan}
                        onChange={(e) => handleInputChange("plan", e.target.value)}
                        placeholder="Brief description of the travel plan"
                        rows={3}
                      />
                    </div>

                    <div>
                      <Label htmlFor="inclusions">Inclusions</Label>
                      <Textarea
                        id="inclusions"
                        value={travelData.inclusions}
                        onChange={(e) => handleInputChange("inclusions", e.target.value)}
                        placeholder="What's included in the package"
                        rows={2}
                      />
                    </div>

                    <div>
                      <Label htmlFor="exclusions">Exclusions</Label>
                      <Textarea
                        id="exclusions"
                        value={travelData.exclusions}
                        onChange={(e) => handleInputChange("exclusions", e.target.value)}
                        placeholder="What's not included"
                        rows={2}
                      />
                    </div>

                    <div>
                      <Label htmlFor="overlays">Special Offers</Label>
                      <Input
                        id="overlays"
                        value={travelData.overlays}
                        onChange={(e) => handleInputChange("overlays", e.target.value)}
                        placeholder="Special offers or highlights"
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Template Preview - Center/Main Content */}
            <div className="flex-1 order-2">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-6">
                  <CardTitle className="flex items-center gap-2">
                    <Layout className="w-5 h-5" />
                    Template Designer
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleAddTextElement}
                      className="h-8 px-3"
                    >
                      ➕ Add Text
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={applyBrandKit}
                      disabled={!hasBrandKit()}
                      className="h-8 px-3"
                      title={hasBrandKit() ? "Apply your brand colors, fonts, and logo" : "Set up your brand kit first"}
                    >
                      <Sparkles className="w-3 h-3 mr-1" />
                      Apply Brand Kit
                    </Button>
                    <Button
                      variant={showGrid ? "default" : "outline"}
                      size="sm"
                      onClick={() => setShowGrid(!showGrid)}
                      className="h-8 px-3"
                    >
                      Grid
                    </Button>
                    <Button
                      variant={showGuidelines ? "default" : "outline"}
                      size="sm"
                      onClick={() => setShowGuidelines(!showGuidelines)}
                      className="h-8 px-3"
                    >
                      Guides
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="flex justify-center p-6 mobile-scroll">
                    <div
                      ref={templateRef}
                      className="relative shadow-lg overflow-hidden w-full max-w-full mx-auto"
                      style={{
                        width: `min(calc(100vw - 3rem), ${Math.floor(currentLayout.displaySize.width * 1.5)}px)`,
                        height: `${Math.floor(currentLayout.displaySize.height * 1.5)}px`,
                        maxWidth: "100%",
                        backgroundColor: currentLayout.backgroundStyle.backgroundColor,
                        backgroundImage: currentLayout.backgroundStyle.backgroundImage
                          ? `url(${currentLayout.backgroundStyle.backgroundImage})`
                          : "none",
                        backgroundSize: "cover",
                        backgroundPosition: "center",
                        aspectRatio: `${currentLayout.displaySize.width} / ${currentLayout.displaySize.height}`,
                      }}
                      onClick={() => setSelectedElement(null)}
                    >
                      {/* Background overlay */}
                      {currentLayout.backgroundStyle.overlay && currentLayout.backgroundStyle.backgroundImage && (
                        <div
                          className="absolute inset-0 bg-black"
                          style={{ opacity: currentLayout.backgroundStyle.overlayOpacity }}
                        />
                      )}

                      {/* Render all elements */}
                      {currentLayout.elements.map((element) => (
                        <DraggableElement
                          key={element.id}
                          element={element}
                          travelData={travelData}
                          selectedElement={selectedElement}
                          isDragging={isDragging}
                          isResizing={isResizing}
                          isExporting={isExporting}
                          currentLayout={currentLayout}
                          onMouseDown={handleMouseDown}
                          onTouchStart={handleTouchStart}
                          onResizeStart={handleResizeStart}
                          onClick={setSelectedElement}
                          onDoubleClick={handleDoubleClick}
                        />
                      ))}

                      {/* Grid Overlay */}
                      {!isExporting && showGrid && (
                        <div className="absolute inset-0 pointer-events-none z-[10]">
                          <svg width="100%" height="100%" className="opacity-30">
                            <defs>
                              <pattern
                                id="grid"
                                width="20"
                                height="20"
                                patternUnits="userSpaceOnUse"
                              >
                                <path
                                  d="M 20 0 L 0 0 0 20"
                                  fill="none"
                                  stroke="#3b82f6"
                                  strokeWidth="0.5"
                                />
                              </pattern>
                            </defs>
                            <rect width="100%" height="100%" fill="url(#grid)" />
                          </svg>
                        </div>
                      )}

                      {/* Center Guidelines */}
                      {!isExporting && showGuidelines && (
                        <div className="absolute inset-0 pointer-events-none z-[11]">
                          {/* Vertical center line */}
                          <div
                            className="absolute bg-pink-400 opacity-40"
                            style={{
                              left: "50%",
                              top: 0,
                              width: "1px",
                              height: "100%",
                              transform: "translateX(-50%)",
                            }}
                          />
                          {/* Horizontal center line */}
                          <div
                            className="absolute bg-pink-400 opacity-40"
                            style={{
                              top: "50%",
                              left: 0,
                              height: "1px",
                              width: "100%",
                              transform: "translateY(-50%)",
                            }}
                          />
                        </div>
                      )}

                      {/* Floating Element Toolbar */}
                      {!isExporting && selectedElement && selectedElementData && !selectedElementData.locked && (
                        <div 
                          className="absolute bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg px-2 py-1 flex items-center gap-1 z-[20]"
                          style={{
                            left: Math.min(
                              selectedElementData.position.x * (Math.floor(currentLayout.displaySize.width * 1.5) / currentLayout.displaySize.width) + selectedElementData.size.width * (Math.floor(currentLayout.displaySize.width * 1.5) / currentLayout.displaySize.width) / 2 - 100,
                              Math.floor(currentLayout.displaySize.width * 1.5) - 200
                            ),
                            top: Math.max(
                              5,
                              selectedElementData.position.y * (Math.floor(currentLayout.displaySize.height * 1.5) / currentLayout.displaySize.height) - 40
                            ),
                          }}
                        >
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDuplicateElement(selectedElement)}
                            className="h-8 w-8 p-0"
                            title="Duplicate"
                          >
                            📄
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setCurrentLayout((prev) => ({
                                ...prev,
                                elements: prev.elements.map((el) =>
                                  el.id === selectedElement
                                    ? { ...el, position: { x: 200 - el.size.width / 2, y: el.position.y } }
                                    : el,
                                ),
                              }))
                            }}
                            className="h-8 w-8 p-0"
                            title="Center"
                          >
                            ⚌
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteElement(selectedElement)}
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                            title="Delete"
                          >
                            🗑️
                          </Button>
                        </div>
                      )}
                      
                      {/* Selection indicator */}
                      {!isExporting && selectedElement && (
                        <div className="absolute top-2 left-2 bg-blue-500 text-white px-2 py-1 rounded text-xs font-medium">
                          {selectedElementData?.type} selected
                        </div>
                      )}
                      
                      {/* Debug Status - Show selection state */}
                      {!isExporting && (
                        <div className="debug-overlay absolute top-2 right-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs pointer-events-none">
                          {isExporting ? "Exporting..." : selectedElement ? `Selected: ${selectedElementData?.type || 'element'}` : "No selection"}
                        </div>
                      )}

                      {/* Text Editing Modal */}
                      {editingElement && (
                        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[30]">
                          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-[90vw]">
                            <h3 className="text-lg font-semibold mb-4">Edit Text</h3>
                            <Textarea
                              value={editingText}
                              onChange={(e) => setEditingText(e.target.value)}
                              className="mb-4 min-h-[100px]"
                              placeholder="Enter your text here..."
                              autoFocus
                            />
                            <div className="flex gap-2 justify-end">
                              <Button
                                variant="outline"
                                onClick={handleTextEditCancel}
                              >
                                Cancel
                              </Button>
                              <Button
                                onClick={handleTextEditSave}
                              >
                                Save
                              </Button>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Template Settings - Mobile: Full width, Desktop: Sidebar */}
            <div className="w-full lg:w-80 lg:flex-shrink-0 order-3">
              <div className="lg:sticky lg:top-24 space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Palette className="w-5 h-5" />
                      {selectedElement ? `Customize ${selectedElementData?.type}` : "Template Settings"}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {selectedElement && selectedElementData ? (
                      <Tabs defaultValue="style" className="w-full">
                        <TabsList className="grid w-full grid-cols-3">
                          <TabsTrigger value="style">Style</TabsTrigger>
                          <TabsTrigger value="layout">Layout</TabsTrigger>
                          <TabsTrigger value="effects">Effects</TabsTrigger>
                        </TabsList>

                        <TabsContent value="style" className="space-y-4">
                          <div>
                            <Label>Font Family</Label>
                            <Select
                              value={selectedElementData.style.fontFamily}
                              onValueChange={(value) => handleElementStyleChange(selectedElement, "fontFamily", value)}
                            >
                              <SelectTrigger className="h-12">
                                <SelectValue>
                                  <span style={{ fontFamily: selectedElementData.style.fontFamily }} className="text-base">
                                    {selectedElementData.style.fontFamily}
                                  </span>
                                </SelectValue>
                              </SelectTrigger>
                              <SelectContent className="max-h-60">
                                {/* Brand Fonts */}
                                {brandKit?.fonts && brandKit.fonts.length > 0 && (
                                  <>
                                    {brandKit.fonts.map((brandFont) => (
                                      <SelectItem key={`brand-${brandFont.id}`} value={brandFont.fontFamily} className="h-12 cursor-pointer">
                                        <div className="flex flex-col items-start">
                                          <div className="flex items-center gap-2">
                                            <span style={{ fontFamily: brandFont.fontFamily }} className="text-base font-medium">
                                              {brandFont.name}
                                            </span>
                                            <Badge variant="secondary" className="text-xs">
                                              {brandFont.type === "headline" ? "Headline" : brandFont.type === "body" ? "Body" : "Brand"}
                                            </Badge>
                                          </div>
                                          <span style={{ fontFamily: brandFont.fontFamily }} className="text-sm text-gray-500">
                                            The quick brown fox jumps
                                          </span>
                                        </div>
                                      </SelectItem>
                                    ))}
                                    <div className="px-2 py-1">
                                      <div className="h-px bg-gray-200 dark:bg-gray-700"></div>
                                    </div>
                                  </>
                                )}
                                {/* Default Fonts */}
                                {/* Expressive & Adventurous Scripts */}
                                <div className="px-2 py-1">
                                  <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Scripts & Handwriting</div>
                                </div>
                                {fontOptions.slice(0, 12).map((font) => (
                                  <SelectItem key={font} value={font} className="h-12 cursor-pointer">
                                    <div className="flex flex-col items-start">
                                      <span style={{ fontFamily: font }} className="text-base font-medium">
                                        {font}
                                      </span>
                                      <span style={{ fontFamily: font }} className="text-sm text-gray-500">
                                        The quick brown fox jumps
                                      </span>
                                    </div>
                                  </SelectItem>
                                ))}
                                
                                {/* Bold & Impactful Display */}
                                <div className="px-2 py-1">
                                  <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Bold & Impactful</div>
                                </div>
                                {fontOptions.slice(12, 24).map((font) => (
                                  <SelectItem key={font} value={font} className="h-12 cursor-pointer">
                                    <div className="flex flex-col items-start">
                                      <span style={{ fontFamily: font }} className="text-base font-medium">
                                        {font}
                                      </span>
                                      <span style={{ fontFamily: font }} className="text-sm text-gray-500">
                                        The quick brown fox jumps
                                      </span>
                                    </div>
                                  </SelectItem>
                                ))}
                                
                                {/* Elegant & Classic Serifs */}
                                <div className="px-2 py-1">
                                  <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Elegant & Classic</div>
                                </div>
                                {fontOptions.slice(24, 36).map((font) => (
                                  <SelectItem key={font} value={font} className="h-12 cursor-pointer">
                                    <div className="flex flex-col items-start">
                                      <span style={{ fontFamily: font }} className="text-base font-medium">
                                        {font}
                                      </span>
                                      <span style={{ fontFamily: font }} className="text-sm text-gray-500">
                                        The quick brown fox jumps
                                      </span>
                                    </div>
                                  </SelectItem>
                                ))}
                                
                                {/* Unique & Thematic */}
                                <div className="px-2 py-1">
                                  <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Unique & Thematic</div>
                                </div>
                                {fontOptions.slice(36, 44).map((font) => (
                                  <SelectItem key={font} value={font} className="h-12 cursor-pointer">
                                    <div className="flex flex-col items-start">
                                      <span style={{ fontFamily: font }} className="text-base font-medium">
                                        {font}
                                      </span>
                                      <span style={{ fontFamily: font }} className="text-sm text-gray-500">
                                        The quick brown fox jumps
                                      </span>
                                    </div>
                                  </SelectItem>
                                ))}
                                
                                {/* Modern & Clean */}
                                <div className="px-2 py-1">
                                  <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Modern & Clean</div>
                                </div>
                                {fontOptions.slice(44).map((font) => (
                                  <SelectItem key={font} value={font} className="h-12 cursor-pointer">
                                    <div className="flex flex-col items-start">
                                      <span style={{ fontFamily: font }} className="text-base font-medium">
                                        {font}
                                      </span>
                                      <span style={{ fontFamily: font }} className="text-sm text-gray-500">
                                        The quick brown fox jumps
                                      </span>
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div>
                            <Label>Font Size</Label>
                            <Input
                              type="range"
                              min="8"
                              max="48"
                              value={selectedElementData.style.fontSize}
                              onChange={(e) =>
                                handleElementStyleChange(selectedElement, "fontSize", Number.parseInt(e.target.value))
                              }
                              className="mt-2"
                            />
                            <span className="text-sm text-gray-500">{selectedElementData.style.fontSize}px</span>
                          </div>

                          <div>
                            <Label>Font Weight</Label>
                            <Select
                              value={selectedElementData.style.fontWeight}
                              onValueChange={(value) => handleElementStyleChange(selectedElement, "fontWeight", value)}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {fontWeights.map((weight) => (
                                  <SelectItem key={weight} value={weight}>
                                    {weight === "300"
                                      ? "Light"
                                      : weight === "400"
                                        ? "Regular"
                                        : weight === "500"
                                          ? "Medium"
                                          : weight === "600"
                                            ? "Semi Bold"
                                            : weight === "700"
                                              ? "Bold"
                                              : "Extra Bold"}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div>
                            <Label>Text Color</Label>
                            <div className="flex gap-2 mt-2">
                              <Input
                                type="color"
                                value={selectedElementData.style.color}
                                onChange={(e) => handleElementStyleChange(selectedElement, "color", e.target.value)}
                                className="w-12 h-10 p-1 border rounded"
                              />
                              <Input
                                value={selectedElementData.style.color}
                                onChange={(e) => handleElementStyleChange(selectedElement, "color", e.target.value)}
                                placeholder="#000000"
                              />
                            </div>
                          </div>

                          <div>
                            <Label>Background Color</Label>
                            <div className="flex gap-2 mt-2">
                              <Input
                                type="color"
                                value={selectedElementData.style.backgroundColor}
                                onChange={(e) =>
                                  handleElementStyleChange(selectedElement, "backgroundColor", e.target.value)
                                }
                                className="w-12 h-10 p-1 border rounded"
                              />
                              <Input
                                value={selectedElementData.style.backgroundColor}
                                onChange={(e) =>
                                  handleElementStyleChange(selectedElement, "backgroundColor", e.target.value)
                                }
                                placeholder="transparent"
                              />
                            </div>
                          </div>

                          <div>
                            <Label className="flex items-center justify-between">
                              AI Color Palettes
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => generateColorPalettes(travelData.destination)}
                                disabled={isLoadingColors || !travelData.destination.trim()}
                                className="h-6 px-2 text-xs"
                              >
                                {isLoadingColors ? "🎨 Generating..." : "✨ Generate"}
                              </Button>
                            </Label>
                            {colorPalettes.length > 0 && (
                              <div className="mt-2 space-y-3">
                                {colorPalettes.slice(0, 3).map((palette, paletteIndex) => (
                                  <div key={paletteIndex} className="space-y-1">
                                    <div className="text-xs font-medium text-gray-600 dark:text-gray-400">
                                      {palette.name}
                                    </div>
                                    <div className="flex gap-1">
                                      {palette.colors.map((color, colorIndex) => (
                                        <div key={colorIndex} className="flex flex-col items-center gap-1">
                                          <button
                                            className="w-8 h-8 rounded border-2 border-gray-200 hover:border-gray-400 transition-colors"
                                            style={{ backgroundColor: color }}
                                            onClick={() => handleElementStyleChange(selectedElement, "color", color)}
                                            title={`Apply ${color} as text color`}
                                          />
                                          <button
                                            className="w-8 h-8 rounded border-2 border-gray-200 hover:border-gray-400 transition-colors"
                                            style={{ backgroundColor: color }}
                                            onClick={() => handleElementStyleChange(selectedElement, "backgroundColor", color)}
                                            title={`Apply ${color} as background color`}
                                          />
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        </TabsContent>

                        <TabsContent value="layout" className="space-y-4">
                          <div>
                            <Label>Opacity</Label>
                            <Input
                              type="range"
                              min="0"
                              max="1"
                              step="0.1"
                              value={selectedElementData.style.opacity}
                              onChange={(e) => handleElementStyleChange(selectedElement, "opacity", Number.parseFloat(e.target.value))}
                            />
                            <span className="text-sm text-gray-500">{Math.round(selectedElementData.style.opacity * 100)}%</span>
                          </div>
                          <div>
                            <Label>Border Radius</Label>
                            <Input
                              type="range"
                              min="0"
                              max="50"
                              value={selectedElementData.style.borderRadius}
                              onChange={(e) => handleElementStyleChange(selectedElement, "borderRadius", Number.parseInt(e.target.value))}
                            />
                            <span className="text-sm text-gray-500">{selectedElementData.style.borderRadius}px</span>
                          </div>
                          <div>
                            <Label>Padding</Label>
                            <Input
                              type="range"
                              min="0"
                              max="40"
                              value={selectedElementData.style.padding}
                              onChange={(e) => handleElementStyleChange(selectedElement, "padding", Number.parseInt(e.target.value))}
                            />
                            <span className="text-sm text-gray-500">{selectedElementData.style.padding}px</span>
                          </div>
                          <div>
                            <Label>Text Align</Label>
                            <div className="flex gap-2 mt-2">
                              {["left", "center", "right"].map((align) => (
                                <Button
                                  key={align}
                                  variant={selectedElementData.style.textAlign === align ? "default" : "outline"}
                                  onClick={() => handleElementStyleChange(selectedElement, "textAlign", align)}
                                >
                                  {align.charAt(0).toUpperCase() + align.slice(1)}
                                </Button>
                              ))}
                            </div>
                          </div>
                        </TabsContent>
                        
                        <TabsContent value="effects" className="space-y-4">
                          <div>
                            <Label>Text Shadow</Label>
                            <Input
                              type="text"
                              value={(selectedElementData.style as any).textShadow || ""}
                              onChange={(e) => handleElementStyleChange(selectedElement, "textShadow", e.target.value)}
                              placeholder="e.g., 2px 2px 4px #000000"
                            />
                          </div>
                          <div>
                            <Label>Box Shadow</Label>
                            <Input
                              type="text"
                              value={(selectedElementData.style as any).boxShadow || ""}
                              onChange={(e) => handleElementStyleChange(selectedElement, "boxShadow", e.target.value)}
                              placeholder="e.g., 0 4px 6px rgba(0,0,0,0.1)"
                            />
                          </div>
                          <div>
                            <Label>Text Transform</Label>
                            <Select
                              value={(selectedElementData.style as any).textTransform || "none"}
                              onValueChange={(value) => handleElementStyleChange(selectedElement, "textTransform", value)}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="none">None</SelectItem>
                                <SelectItem value="uppercase">Uppercase</SelectItem>
                                <SelectItem value="lowercase">Lowercase</SelectItem>
                                <SelectItem value="capitalize">Capitalize</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div>
                            <Label>Letter Spacing</Label>
                            <Input
                              type="text"
                              value={(selectedElementData.style as any).letterSpacing || "normal"}
                              onChange={(e) => handleElementStyleChange(selectedElement, "letterSpacing", e.target.value)}
                              placeholder="e.g., 2px or 0.1em"
                            />
                          </div>
                          <div>
                            <Label>Line Height</Label>
                            <Input
                              type="text"
                              value={(selectedElementData.style as any).lineHeight || "normal"}
                              onChange={(e) => handleElementStyleChange(selectedElement, "lineHeight", e.target.value)}
                              placeholder="e.g., 1.5 or 24px"
                            />
                          </div>
                        </TabsContent>
                      </Tabs>
                    ) : (
                      <div className="space-y-4">
                        <div>
                          <Label>Background Image</Label>
                          <div className="grid grid-cols-2 gap-2 mt-2">
                            {uploadedImages.map((img, index) => (
                              <button key={index} onClick={() => handleBackgroundStyleChange("backgroundImage", img)} className="relative aspect-video rounded-lg overflow-hidden border-2 border-transparent hover:border-blue-500">
                                <img src={img} alt={`Uploaded ${index}`} className="w-full h-full object-cover" />
                              </button>
                            ))}
                            <button onClick={() => fileInputRef.current?.click()} className="flex flex-col items-center justify-center aspect-video rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                              <Upload className="w-6 h-6 mb-1" />
                              <span className="text-xs">Upload Image</span>
                            </button>
                            <input
                              type="file"
                              ref={fileInputRef}
                              onChange={handleImageUpload}
                              className="hidden"
                              accept="image/*"
                            />
                          </div>
                        </div>

                        <div>
                          <Label className="flex items-center justify-between">
                            AI Image Suggestions
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => fetchImageSuggestions(travelData.destination)}
                              disabled={isLoadingSuggestions || !travelData.destination.trim()}
                              className="h-6 px-2 text-xs"
                            >
                              {isLoadingSuggestions ? "🖼️ Searching..." : "✨ Generate"}
                            </Button>
                          </Label>
                          {suggestedImages.length > 0 && (
                            <div className="grid grid-cols-2 gap-2 mt-2">
                              {suggestedImages.map((img, index) => (
                                <button key={index} onClick={() => handleBackgroundStyleChange("backgroundImage", img.url)} className="relative aspect-video rounded-lg overflow-hidden border-2 border-transparent hover:border-blue-500">
                                  <img src={img.url} alt={img.alt} className="w-full h-full object-cover" />
                                </button>
                              ))}
                            </div>
                          )}
                        </div>
                        
                        <div>
                          <Label>Background Color</Label>
                          <div className="flex gap-2 mt-2">
                            <Input
                              type="color"
                              value={currentLayout.backgroundStyle.backgroundColor}
                              onChange={(e) => handleBackgroundStyleChange("backgroundColor", e.target.value)}
                              className="w-12 h-10 p-1 border rounded"
                            />
                            <Input
                              value={currentLayout.backgroundStyle.backgroundColor}
                              onChange={(e) => handleBackgroundStyleChange("backgroundColor", e.target.value)}
                              placeholder="#ffffff"
                            />
                          </div>
                        </div>

                        <div>
                          <Label className="flex items-center justify-between">
                            Background Overlay
                            <Switch
                              checked={currentLayout.backgroundStyle.overlay}
                              onCheckedChange={(checked) => handleBackgroundStyleChange("overlay", checked)}
                            />
                          </Label>
                          {currentLayout.backgroundStyle.overlay && (
                            <div className="mt-2">
                              <Label>Overlay Opacity</Label>
                              <Input
                                type="range"
                                min="0"
                                max="1"
                                step="0.1"
                                value={currentLayout.backgroundStyle.overlayOpacity}
                                onChange={(e) => handleBackgroundStyleChange("overlayOpacity", Number.parseFloat(e.target.value))}
                              />
                              <span className="text-sm text-gray-500">{Math.round(currentLayout.backgroundStyle.overlayOpacity * 100)}%</span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}